{"trafficInsuranceOrVehicleLicense": true, "carInvoiceOrVehicleLicense": false, "serviceEnableDateReadonly": true, "fieldMapping": [{"name": "证件类型", "field": "certificateType", "isShow": 2}, {"name": "证件图片", "field": "certificateImgList", "isShow": 2}, {"name": "车主姓名", "field": "customerName", "isShow": 2}, {"name": "证件号", "field": "certificateNo", "isShow": 2}, {"name": "联系电话", "field": "contactTelephone", "isShow": 2}, {"name": "行驶证图片", "field": "vehicleLicenseImgList", "isShow": 1}, {"name": "车架号", "field": "vinNo", "isShow": 2}, {"name": "发动机号", "field": "engineNo", "isShow": 2}, {"name": "车牌号码", "field": "carNo", "isShow": 1}, {"name": "注册日期", "field": "registrationDate", "isShow": 1}, {"name": "发证日期", "field": "issueDate", "isShow": 1}, {"name": "使用性质", "field": "carUseage", "isShow": 2}, {"name": "车辆品牌", "field": "carBrandId", "isShow": 2}, {"name": "车辆型号", "field": "carSeriesName", "isShow": 2}, {"name": "动力类型", "field": "powerType", "isShow": 2}, {"name": "购车发票", "field": "carInvoiceImgList", "isShow": 2}, {"name": "购车金额", "field": "carPrice", "isShow": 2}, {"name": "购车日期", "field": "carBuyDate", "isShow": 2}, {"name": "汽车图片", "field": "carImagesList", "isShow": 2}, {"name": "行驶里程图片", "field": "carMileageImgList", "isShow": 2}, {"name": "车辆行驶里程", "field": "carMileage", "isShow": 2}, {"name": "交强险保单图片", "field": "trafficInsuranceImgList", "isShow": 1}, {"name": "车船税金额", "field": "vehicleTaxPrice", "isShow": 1}, {"name": "完税证明", "field": "purchaseTaxCompleteImgList", "isShow": 1}, {"name": "购置税金额", "field": "purchaseTaxPrice", "isShow": 2}, {"name": "上牌费用发票图片", "field": "vehicleLicenseInvoiceImgList", "isShow": 1}, {"name": "上牌费用", "field": "vehicleLicensePrice", "isShow": 2}, {"name": "商业险生效日期", "field": "commercialInsuranceEnableDate", "isShow": 0}, {"name": "服务生效日期", "field": "serviceEnableDate", "isShow": 2}, {"name": "服务期限", "field": "productTermId", "isShow": 2}, {"name": "服务合同图片", "field": "serviceContractImgList", "isShow": 0}, {"name": "经销商", "field": "storeId", "isShow": 2}, {"name": "付款方式", "field": "payType", "isShow": 2}, {"name": "付款金额", "field": "payPrice", "isShow": 2}, {"name": "付款小票图片", "field": "payImgList", "isShow": 1}, {"name": "分期数", "field": "payPeriod", "isShow": 2}, {"name": "分期首期日期", "field": "payFirstDate", "isShow": 2}]}