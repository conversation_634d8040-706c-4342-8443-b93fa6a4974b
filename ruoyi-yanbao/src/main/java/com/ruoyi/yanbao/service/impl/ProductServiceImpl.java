package com.ruoyi.yanbao.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.exception.base.BaseException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.yanbao.entity.InsuranceCompany;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.ProductTerm;
import com.ruoyi.yanbao.entity.ProductType;
import com.ruoyi.yanbao.entity.Store;
import com.ruoyi.yanbao.entity.vo.ProductVo;
import com.ruoyi.yanbao.mapper.InsuranceCompanyMapper;
import com.ruoyi.yanbao.mapper.ProductMapper;
import com.ruoyi.yanbao.mapper.ProductTermMapper;
import com.ruoyi.yanbao.mapper.ProductTypeMapper;
import com.ruoyi.yanbao.mapper.StoreMapper;
import com.ruoyi.yanbao.service.ProductService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Autowired
    private ProductTermMapper productTermMapper;

    @Autowired
    private ProductTypeMapper productTypeMapper;

    @Autowired
    private InsuranceCompanyMapper insuranceCompanyMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Override
    public List<Product> getAvailableProducts(String storeIds) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getStatus, Constants.STATUS_ENABLE);
        queryWrapper.select(Product::getId, Product::getName, Product::getRemark, Product::getSuitStoreIds,
                Product::getType);
        queryWrapper.orderByDesc(Product::getSort);
        List<Product> products = list(queryWrapper);
        if (StringUtils.isNotEmpty(storeIds)) {
            List<String> storeIdList = Arrays.asList(storeIds.split(","));
            products = products.stream().filter(p -> {
                if (StringUtils.isNotEmpty(p.getSuitStoreIds())) {
                    String[] storeIdArray = p.getSuitStoreIds().split(",");
                    for (String storeId : storeIdArray) {
                        if (storeIdList.contains(storeId)) {
                            return true;
                        }
                    }
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
        }
        return products;
    }


    @Override
    public ProductVo getProductInfo(Long productId) {
        if (productId == null) {
            return null;
        }
        Product product = getById(productId);
        if (product == null || !Constants.STATUS_ENABLE.equals(product.getStatus())) {
            return null;
        }
        ProductVo productVo = new ProductVo();
        BeanUtils.copyProperties(product, productVo);
        List<ProductTerm> productTerms =
                productTermMapper.selectList(new LambdaQueryWrapper<ProductTerm>().eq(ProductTerm::getProductId, productId));
        productVo.setTerms(productTerms);
        ProductType productType = productTypeMapper.selectOne(new LambdaQueryWrapper<ProductType>().eq(ProductType::getType, product.getType()));
        if (productType == null) {
            throw new BaseException("产品类型不存在");
        }
        JSONObject frontSetting = JSONObject.parseObject(productType.getFrontSetting());
        List<String> fields = new ArrayList<>();
        JSONObject rules = new JSONObject();
        if (frontSetting.getObject("fieldMapping", JSONArray.class) != null)
            frontSetting.getJSONArray("fieldMapping").forEach(item -> {
                JSONObject field = (JSONObject) item;
                if (field.getInteger("isShow") != null && (field.getInteger("isShow") == 1 || field.getInteger("isShow") == 2)) {
                    fields.add(field.getString("field"));
                    if (field.getInteger("isShow") == 2) {
                        JSONArray ruleList = new JSONArray();
                        ruleList.add(JSONObject.parseObject("{\"required\": true, \"message\": \"" + field.getString("name") + "不能为空\", \"trigger\": \"change\"}"));
                        rules.put(field.getString("name"), ruleList);
                    }
                }
            });
        frontSetting.put("fields", fields);
        frontSetting.put("rules", rules);
        productType.setFrontSetting(frontSetting.toJSONString());
        productVo.setProductType(productType);
        return productVo;
    }

    @Override
    public List<Product> selectProductList(Product product) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();

        if (product.getType() != null) {
            queryWrapper.eq(Product::getType, product.getType());
        }
        if (StringUtils.isNotEmpty(product.getName())) {
            queryWrapper.like(Product::getName, product.getName());
        }
        if (product.getStatus() != null) {
            queryWrapper.eq(Product::getStatus, product.getStatus());
        }
        if (product.getSalesMode() != null) {
            queryWrapper.eq(Product::getSalesMode, product.getSalesMode());
        }
        if (product.getInsuranceCompanyId() != null) {
            queryWrapper.eq(Product::getInsuranceCompanyId, product.getInsuranceCompanyId());
        }

        queryWrapper.orderByDesc(Product::getSort).orderByDesc(Product::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<ProductVo> selectProductListWithDetails(Product product) {
        // 先查询产品列表
        List<Product> products = selectProductList(product);

        // 转换为ProductListVo并填充关联信息
        return products.stream().map(p -> {
            ProductVo vo = new ProductVo();
            BeanUtils.copyProperties(p, vo);
            // 填充保险公司名称
            if (p.getInsuranceCompanyId() != null) {
                InsuranceCompany insuranceCompany = insuranceCompanyMapper.selectById(p.getInsuranceCompanyId());
                if (insuranceCompany != null) {
                    vo.setInsuranceCompanyName(insuranceCompany.getName());
                }
            }
            // 填充适用门店名称
            if (StringUtils.isNotEmpty(p.getSuitStoreIds())) {
                String[] storeIds = p.getSuitStoreIds().split(",");
                List<Store> stores = storeMapper.selectBatchIds(Arrays.asList(storeIds));
                List<String> storeNames = stores.stream().map(Store::getName).toList();
                vo.setSuitStoreNames(storeNames);
                vo.setSuitStoreNamesStr(String.join(", ", storeNames));
            } else {
                vo.setSuitStoreNamesStr("全部门店");
            }

            return vo;
        }).toList();
    }

    @Override
    public boolean changeStatus(Long id, Integer status) {
        Product product = new Product();
        product.setId(id);
        product.setStatus(status);
        return this.updateById(product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveProductWithTerms(ProductVo product) {
        checkTerms(product);
        // 保存产品
        boolean result = this.save(product);
        if (result && CollectionUtils.isNotEmpty(product.getTerms())) {
            // 设置产品ID并保存保障期限
            for (ProductTerm term : product.getTerms()) {
                term.setProductId(product.getId());
                term.setCreatedAt(new Date());
                term.setCreatedBy(product.getCreatedBy());
                term.setIsDelete(Constants.NOT_DELETE);
            }
            result = productTermMapper.insertBatch(product.getTerms()) == product.getTerms().size();
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductWithTerms(ProductVo product) {
        checkTerms(product);
        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<Product>();
        updateWrapper.eq(Product::getId, product.getId());
        updateWrapper.set(Product::getName, product.getName())
                .set(Product::getRemark, product.getRemark())
                .set(Product::getSalesMode, product.getSalesMode())
                .set(Product::getMaximumMileageAvailable, product.getMaximumMileageAvailable())
                .set(Product::getMaximumPeriodAvailable, product.getMaximumPeriodAvailable())
                .set(Product::getMaximumPeriodAvailableType, product.getMaximumPeriodAvailableType())
                .set(Product::getFranchisePeriod, product.getFranchisePeriod())
                .set(Product::getStatus, product.getStatus())
                .set(Product::getBuyLimit, product.getBuyLimit())
                .set(Product::getAllowInstallmentPayment, product.getAllowInstallmentPayment())
                .set(Product::getInsuranceCompanyId, product.getInsuranceCompanyId())
                .set(Product::getSuitStoreIds, product.getSuitStoreIds())
                .set(Product::getSort, product.getSort())
                .set(Product::getChangedBy, product.getChangedBy())
                .set(Product::getChangedAt, new Date());
        // 更新产品
        boolean result = this.update(updateWrapper);
        //更新产品期限，有ID的更新，没ID的新增，再删除
        if (result) {
            List<ProductTerm> oldList = productTermMapper.selectList(new LambdaQueryWrapper<ProductTerm>().eq(ProductTerm::getProductId, product.getId()));
            if (CollectionUtils.isNotEmpty(oldList)) {
                List<Long> oldIds = oldList.stream().map(ProductTerm::getId).toList();
                oldIds = oldIds.stream().filter(id -> product.getTerms().stream().noneMatch(term -> term.getId() != null && term.getId().equals(id))).collect(Collectors.toList());
                if (!oldIds.isEmpty()) {
                    productTermMapper.deleteBatchIds(oldIds);
                }
                // 更新
                for (ProductTerm term : product.getTerms()) {
                    if (term.getId() != null) {
                        LambdaUpdateWrapper<ProductTerm> productTermUpdateWrapper = new LambdaUpdateWrapper<ProductTerm>();
                        productTermUpdateWrapper.eq(ProductTerm::getId, term.getId());
                        productTermUpdateWrapper.set(ProductTerm::getName, term.getName())
                                .set(ProductTerm::getTimeLimit, term.getTimeLimit())
                                .set(ProductTerm::getMileage, term.getMileage())
                                .set(ProductTerm::getChangedBy, product.getChangedBy())
                                .set(ProductTerm::getChangedAt, new Date());
                        productTermMapper.update(productTermUpdateWrapper);
                    } else {
                        term.setProductId(product.getId());
                        term.setCreatedAt(new Date());
                        term.setCreatedBy(product.getCreatedBy());
                        term.setIsDelete(Constants.NOT_DELETE);
                        productTermMapper.insert(term);
                    }
                }

            }
        }
        return result;
    }

    private void checkTerms(ProductVo product) {
        if (CollectionUtils.isEmpty(product.getTerms())) {
            throw new BaseException("保障期限不能为空");
        }
        Set<String> names = new HashSet<>();
        for (ProductTerm productTerm : product.getTerms()) {
            if (productTerm.getTimeLimit() == null) {
                throw new BaseException("期限不能为空");
            }
            if (StringUtils.isBlank(product.getName())) {
                throw new BaseException("期限名称不能为空");
            }
            names.add(productTerm.getName().trim());
        }
        if (!(names.size() == product.getTerms().size())) {
            throw new BaseException("期限名称不能重复");
        }
    }
}
